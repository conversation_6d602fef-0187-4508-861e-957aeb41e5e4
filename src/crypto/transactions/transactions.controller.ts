import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { TransactionsService } from './transactions.service';
import {
  GetAuthData,
  CustomerAuthGuard,
  CustomerAuthData,
} from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  CreateTransactionDto,
  FundTransactionDto,
} from '../dtos/transaction.dto';

@Controller('transactions')
@UseGuards(CustomerAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('transactions')
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}
  @Post('/fund')
  async fundCryptoWallet(
    @GetAuthData() auth: CustomerAuthData,
    @Body() createTransactionDto: FundTransactionDto,
  ) {
    return this.transactionsService.initfundCryptoWallet(
      createTransactionDto,
      auth,
    );
  }

  @Post('/transfer')
  async transferToExternalWallet(
    @GetAuthData() auth: CustomerAuthData,
    @Body() createTransactionDto: CreateTransactionDto,
  ) {
    return this.transactionsService.transferToExternalWallet(
      createTransactionDto,
      auth,
    );
  }

  @Post('/withdrawal')
  async withdrawIntoCashWallet(
    @GetAuthData() auth: CustomerAuthData,
    @Body() createTransactionDto: FundTransactionDto,
  ) {
    return this.transactionsService.initiateWithdrawal(
      createTransactionDto,
      auth,
    );
  }
}
