import { Column, <PERSON>tity, <PERSON>To<PERSON>ne, OneToOne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { User } from './user.entity';
import { BaseEntity } from '../../config/repository/base-entity';
import { SwapQuotation } from './swap-quotation.entity';

export enum SwapTransactionStatus {
  INITIATED = 'initiated',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REVERSED = 'reversed',
}

@Entity('swap_transactions')
export class SwapTransaction extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @OneToOne(() => SwapQuotation, { nullable: true })
  @JoinColumn()
  quotation: SwapQuotation;

  @Column()
  from_currency: string;

  @Column()
  to_currency: string;

  @Column()
  from_amount: string;

  @Column({ nullable: true })
  received_amount: string;

  @Column({ nullable: true })
  execution_price: string;

  @Column({
    type: 'enum',
    enum: SwapTransactionStatus,
    default: SwapTransactionStatus.INITIATED,
  })
  status: SwapTransactionStatus;
}
