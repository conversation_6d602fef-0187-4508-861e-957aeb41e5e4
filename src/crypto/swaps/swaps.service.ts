import { BadRequestException, Injectable } from '@nestjs/common';
import { SwapTransactionRepository } from '../repositories/swap-transaction.repository';
import { SwapQuotationRepository } from '../repositories/swap-quotation.repository';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxService, SwapQuotation } from '@app/quidax';
import { CreateSwapDto } from '../dtos/swaps.dto';
import { CustomerAuthData } from '@crednet/authmanager';
import { RedisService } from '@crednet/utils';
import { SwapTransaction, SwapTransactionStatus } from '../entities/swap-transaction.entity';

@Injectable()
export class SwapsService {
  constructor(
    private readonly swapTransactionRepository: SwapTransactionRepository,
    private readonly swapQuotationRepository: SwapQuotationRepository,
    private readonly userRepository: UserRepository,
    private readonly quidaxService: QuidaxService,
    private readonly redisService: RedisService,
  ) {}

  async createSwap(
    createSwapDto: CreateSwapDto,
    auth: CustomerAuthData,
  ): Promise<SwapQuotation> {
    try {
      const user = await this.userRepository.getUserByUserId(auth.id);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const response = await this.quidaxService.initiateInstantSwap(user.id, {
        from_currency: createSwapDto.fromCurrency,
        to_currency: createSwapDto.toCurrency,
        from_amount: createSwapDto.fromAmount,
      });

      if (response.status == 'error') {
        throw new BadRequestException(response.message);
      }

      const confirmed = response.data.confirmed === false ? 0 : 1;

      await this.redisService
        .getClient()
        .HSET(`crypto:swap:quotation:${auth.id}:${response.data.id}`, {
          id: response.data.id,
          fromCurrency: response.data.from_currency,
          toCurrency: response.data.to_currency,
          quoted_price: response.data.quoted_price,
          quoted_currency: response.data.quoted_currency,
          fromAmount: response.data.from_amount,
          toAmount: response.data.to_amount,
          confirmed: confirmed,
          createdAt: response.data.created_at,
          expiresAt: response.data.expires_at,
          updatedAt: response.data.updated_at,
          userId: auth.id,
        });

      await this.redisService
        .getClient()
        .EXPIRE(`crypto:swap:quotation:${auth.id}:${response.data.id}`, 300);

      return response.data;
    } catch (error) {
      console.error('Error creating swap quotation:', error);
      throw new BadRequestException('An error occurred while creating swap');
    }
  }

  async getAllSwapQuotations(auth: CustomerAuthData): Promise<any[]> {
    try {
      const keys = await this.redisService
        .getClient()
        .KEYS(`crypto:swap:quotation:${auth.id}:*`);

      const quotations = await Promise.all(
        keys.map(async (key) => {
          const data = await this.redisService.getClient().HGETALL(key);
          return data;
        }),
      );

      return quotations;
    } catch (error) {
      console.error('Error fetching swap quotations:', error);
      throw new BadRequestException(
        'An error occurred while fetching quotations',
      );
    }
  }

  async getSwapQuotation(
    auth: CustomerAuthData,
    quotationId: string,
  ): Promise<any> {
    const quotation = await this.redisService
      .getClient()
      .HGETALL(`crypto:swap:quotation:${auth.id}:${quotationId}`);

    if (!quotation) {
      throw new BadRequestException('Quotation not found');
    }

    return quotation;
  }

  async refreshSwapQuotation(
    auth: CustomerAuthData,
    quotationId: string,
    createSwapDto: CreateSwapDto,
  ): Promise<any> {
    try {
      const user = await this.userRepository.getUserByUserId(auth.id);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const response = await this.quidaxService.refreshSwapQuotation(
        user.id,
        quotationId,
        {
          from_currency: createSwapDto.fromCurrency,
          to_currency: createSwapDto.toCurrency,
          from_amount: createSwapDto.fromAmount,
        },
      );

      if (response.status == 'error') {
        throw new BadRequestException(response.message);
      }

      const confirmed = response.data.confirmed === false ? 0 : 1;

      await this.redisService
        .getClient()
        .HSET(`crypto:swap:quotation:${auth.id}:${response.data.id}`, {
          id: response.data.id,
          fromCurrency: response.data.from_currency,
          toCurrency: response.data.to_currency,
          quoted_price: response.data.quoted_price,
          quoted_currency: response.data.quoted_currency,
          fromAmount: response.data.from_amount,
          toAmount: response.data.to_amount,
          confirmed: confirmed,
          createdAt: response.data.created_at,
          expiresAt: response.data.expires_at,
          updatedAt: response.data.updated_at,
          userId: response.data.user.id,
        });

      await this.redisService
        .getClient()
        .EXPIRE(`crypto:swap:quotation:${auth.id}:${response.data.id}`, 300);

      return response.data;
    } catch (error) {
      console.error('Error refreshing swap quotation:', error);
      throw new BadRequestException('An error occurred while refreshing swap');
    }
  }

  async temporarySwapQuotation(
    auth: CustomerAuthData,
    createSwapDto: CreateSwapDto,
  ): Promise<any> {
    try {
      const user = await this.userRepository.getUserByUserId(auth.id);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const response = await this.quidaxService.temporarySwapQuotation(
        user.id,
        {
          from_currency: createSwapDto.fromCurrency,
          to_currency: createSwapDto.toCurrency,
          from_amount: createSwapDto.fromAmount,
        },
      );

      if (response.status == 'error') {
        throw new BadRequestException(response.message);
      }

      return response;
    } catch (error) {
      console.error('Error creating temporary swap quotation:', error);
      throw new BadRequestException(
        'An error occurred while creating temporary swap',
      );
    }
  }

  async confirmSwapTransaction(
    auth: CustomerAuthData,
    quotationId: string,
  ): Promise<any> {
    try {
      const user = await this.userRepository.getUserByUserId(auth.id);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const response = await this.quidaxService.confirmInstantSwap(
        user.id,
        quotationId,
      );

      if (response.status == 'error') {
        throw new BadRequestException(response.message);
      }

      const createSwapQuotation =
        await this.swapQuotationRepository.createSwapQuotation({
          id: response.data.swap_quotation.id,
          userId: auth.id,
          from_currency: response.data.swap_quotation.from_currency,
          to_currency: response.data.swap_quotation.to_currency,
          quoted_price: response.data.swap_quotation.quoted_price,
          quoted_currency: response.data.swap_quotation.quoted_currency,
          from_amount: response.data.swap_quotation.from_amount,
          to_amount: response.data.swap_quotation.to_amount,
          confirmed: response.data.swap_quotation.confirmed,
          expires_at: response.data.swap_quotation.expires_at,
        });

      const createSwapTransaction =
        await this.swapTransactionRepository.createSwapTransaction({
          id: response.data.id,
          userId: auth.id,
          quotationId: createSwapQuotation.id,
          from_currency: response.data.from_currency,
          to_currency: response.data.to_currency,
          from_amount: response.data.from_amount,
          received_amount: response.data.received_amount,
          execution_price: response.data.execution_price,
        });

      return createSwapTransaction;
    } catch (error) {
      console.error('Error confirming swap transaction:', error);
      throw new BadRequestException('An error occurred while confirming swap');
    }
  }

  async updateSwapTransactionStatus(
    swapTransactionId: string,
    data: any,
  ): Promise<SwapTransaction> {
    try {
      const transaction =
        await this.swapTransactionRepository.getSwapTransaction(
          swapTransactionId,
        );

      transaction.status = data.status;
      if (data.received_amount) {
        transaction.received_amount = data.received_amount;
      }
      if (data.execution_price) {
        transaction.execution_price = data.execution_price;
      }

      return await this.swapTransactionRepository.save(transaction);
    } catch (error) {
      console.error('Error updating swap transaction status:', error);
    }
  }

  async verifySwapTransaction(swapTransactionId: string): Promise<any> {
    try {
      const transaction =
        await this.swapTransactionRepository.getSwapTransaction(
          swapTransactionId,
        );

      const response = await this.quidaxService.fetchSwapTransaction(
        transaction.user.id,
        transaction.id,
      );

      if (response.status == 'error') {
        throw new BadRequestException(response.message);
      }

      this.updateSwapTransactionStatus(swapTransactionId, response.data);

      // todo add notifications for each status change case
    } catch (error) {
      console.error('Error verifying swap transaction:', error);
      throw new BadRequestException(
        'An error occurred while verifying swap transaction',
      );
    }
  }

  async requerySwapTransaction(pageNumber: number) {
    console.log('running job:: verifySwapTransaction ', pageNumber);
    const items = await this.swapTransactionRepository.findMany(
      {
        page: pageNumber,
        limit: 10,
      },
      {
        where: {
          status: SwapTransactionStatus.INITIATED,
        },
      },
    );

    for (const item of items.items) {
      await this.verifySwapTransaction(item.id);
    }

    if (pageNumber < items.meta.totalPages) {
      return this.requerySwapTransaction(++pageNumber);
    }
  }
}
