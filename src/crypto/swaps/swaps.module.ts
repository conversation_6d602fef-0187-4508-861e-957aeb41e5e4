import { Modu<PERSON> } from '@nestjs/common';
import { SwapsService } from './swaps.service';
import { SwapsController } from './swaps.controller';
import { SwapTransactionRepository } from '../repositories/swap-transaction.repository';
import { SwapQuotationRepository } from '../repositories/swap-quotation.repository';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxModule } from '@app/quidax';

@Module({
  imports: [QuidaxModule],
  controllers: [SwapsController],
  providers: [
    SwapsService,
    SwapTransactionRepository,
    SwapQuotationRepository,
    UserRepository,
  ],
})
export class SwapsModule {}
